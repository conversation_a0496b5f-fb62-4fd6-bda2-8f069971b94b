import asyncio
from datetime import datetime, timezone

from memory_manager import MemoryManager
from config.config import config

# Import Graphiti components
from graphiti_core import Graphiti
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.llm_client.openai_client import OpenA<PERSON>lient
from graphiti_core.embedder.openai import <PERSON><PERSON><PERSON>mbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient
from graphiti_core.nodes import EpisodeType


class GraphitiManager(MemoryManager):

    def __init__(self, data_path=None, batch_size=10, is_graph=False, output_path="output.json"):
        # Initialize the parent class
        super().__init__(data_path, batch_size, is_graph, output_path)

        # Single Graphiti instance (official recommended approach)
        self.graphiti = None
        self._initialized = False

        # Initialize the event loop for async operations
        self.loop = None

    def _get_event_loop(self):
        """Get or create an event loop for async operations"""
        if self.loop is None:
            try:
                self.loop = asyncio.get_event_loop()
            except RuntimeError:
                self.loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.loop)
        return self.loop

    def _init_memory_client(self):
        """Initialize the Graphiti client with configuration from config.py"""
        # Extract configuration
        llm_config_data = config['llm']['config']
        embedder_config_data = config['embedder']['config']
        graph_store_config = config['graph_store']['config']

        # Create LLM configuration
        llm_config = LLMConfig(
            api_key=llm_config_data['api_key'],
            model=llm_config_data['model'],
            small_model=llm_config_data.get('model', llm_config_data['model']),  # Use same model for small model if not specified
            base_url=llm_config_data['openai_base_url'],
            temperature=llm_config_data.get('temperature', 0.1)
        )

        # Create LLM client
        llm_client = OpenAIClient(config=llm_config)

        # Create embedder configuration
        embedder_config = OpenAIEmbedderConfig(
            api_key=embedder_config_data['api_key'],
            embedding_model=embedder_config_data['model'],
            embedding_dim=embedder_config_data['embedding_dims'],
            base_url=embedder_config_data['openai_base_url']
        )

        # Create embedder
        embedder = OpenAIEmbedder(config=embedder_config)

        # Create cross encoder (reranker)
        cross_encoder = OpenAIRerankerClient(
            config=llm_config,
            client=llm_client
        )

        # Neo4j connection details
        neo4j_uri = graph_store_config['url'].replace('neo4j://', 'bolt://')
        neo4j_user = graph_store_config['username']
        neo4j_password = graph_store_config['password']

        # Create Graphiti instance
        graphiti = Graphiti(
            neo4j_uri,
            neo4j_user,
            neo4j_password,
            llm_client=llm_client,
            embedder=embedder,
            cross_encoder=cross_encoder
        )

        return graphiti

    def _ensure_initialized(self):
        """Ensure Graphiti is initialized (official single-instance approach)"""
        if not self._initialized:
            self.graphiti = self._init_memory_client()

            # Initialize the graph database indices and constraints
            loop = self._get_event_loop()
            try:
                loop.run_until_complete(
                    self.graphiti.build_indices_and_constraints()
                )
                self._initialized = True
                print("Graphiti initialized successfully")
            except Exception as e:
                print(f"Warning: Could not build indices and constraints: {e}")
                self._initialized = True  # Continue anyway

    def _add_memory(self, message, user_id, metadata):
        """Add memory to Graphiti knowledge graph using best practices"""
        self._ensure_initialized()
        loop = self._get_event_loop()

        try:
            # Determine episode type and content based on message format
            if isinstance(message, list):
                # Handle conversation messages - use EpisodeType.message for better extraction
                episode_content = ""
                episode_type = EpisodeType.message

                for msg in message:
                    if isinstance(msg, dict):
                        # Handle structured message format
                        # todo
                        role = msg.get('role', 'user')
                        content = msg.get('content', str(msg))
                        speaker = user_id if role == 'user' else 'assistant'
                        episode_content += f"{speaker}: {content}\n"
                    elif isinstance(msg, str):
                        # Simple string message
                        episode_content += f"{user_id}: {msg}\n"
                    else:
                        episode_content += f"{user_id}: {str(msg)}\n"

                # Remove trailing newline
                episode_content = episode_content.rstrip('\n')
            else:
                # Single message - use text type
                episode_content = str(message)
                episode_type = EpisodeType.text

            # Get timestamp from metadata
            timestamp = metadata.get('timestamp', datetime.now(timezone.utc).isoformat())
            if isinstance(timestamp, str):
                try:
                    reference_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                except ValueError:
                    reference_time = datetime.now(timezone.utc)
            else:
                reference_time = datetime.now(timezone.utc)

            # Add episode to Graphiti with group_id for user isolation (official approach)
            result = loop.run_until_complete(
                self.graphiti.add_episode(
                    name=f"Conversation_{user_id}_{reference_time.strftime('%Y%m%d_%H%M%S')}",
                    episode_body=episode_content,
                    source=episode_type,
                    source_description=f"User {user_id} conversation memory",
                    reference_time=reference_time,
                    group_id=f"user_{user_id}"  # Official user isolation approach
                )
            )

            return {"status": "success", "episode_id": str(result) if result else None}

        except Exception as e:
            print(f"Error adding memory to Graphiti: {e}")
            raise e

    def _add_memory_bulk(self, messages_batch, user_id, metadata_batch):
        """Add multiple memories efficiently using add_episode_bulk"""
        self._ensure_initialized()
        loop = self._get_event_loop()

        try:
            from graphiti_core.utils.bulk_utils import RawEpisode

            # Prepare episodes for bulk loading
            bulk_episodes = []
            user_group_id = f"user_{user_id}"

            for i, (message, metadata) in enumerate(zip(messages_batch, metadata_batch)):
                # Process each message similar to _add_memory
                if isinstance(message, list):
                    episode_content = ""
                    episode_type = EpisodeType.message

                    for msg in message:
                        if isinstance(msg, dict):
                            # todo
                            role = msg.get('role', 'user')
                            content = msg.get('content', str(msg))
                            speaker = user_id if role == 'user' else 'assistant'
                            episode_content += f"{speaker}: {content}\n"
                        elif isinstance(msg, str):
                            episode_content += f"{user_id}: {msg}\n"
                        else:
                            episode_content += f"{user_id}: {str(msg)}\n"

                    episode_content = episode_content.rstrip('\n')
                else:
                    episode_content = str(message)
                    episode_type = EpisodeType.text

                # Get timestamp
                timestamp = metadata.get('timestamp', datetime.now(timezone.utc).isoformat())
                if isinstance(timestamp, str):
                    try:
                        reference_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    except ValueError:
                        reference_time = datetime.now(timezone.utc)
                else:
                    reference_time = datetime.now(timezone.utc)

                # Create RawEpisode for bulk loading
                episode = RawEpisode(
                    name=f"Bulk_Conversation_{user_id}_{i}_{reference_time.strftime('%Y%m%d_%H%M%S')}",
                    content=episode_content,
                    source=episode_type,
                    source_description=f"User {user_id} bulk conversation memory",
                    reference_time=reference_time,
                    group_id=user_group_id
                )
                bulk_episodes.append(episode)

            # Bulk load episodes
            result = loop.run_until_complete(
                self.graphiti.add_episode_bulk(bulk_episodes)
            )

            return {"status": "success", "episodes_added": len(bulk_episodes)}

        except Exception as e:
            print(f"Error adding bulk memories to Graphiti: {e}")
            raise e

    def add_memories_for_speaker(self, speaker, messages, timestamp, desc):
        """Override parent method to use bulk processing when beneficial"""
        # Use bulk processing for large batches (>= 5 messages)
        if len(messages) >= 5:
            try:
                # Prepare metadata for all messages
                metadata_batch = [{"timestamp": timestamp} for _ in messages]

                # Use bulk processing
                result = self._add_memory_bulk(messages, speaker, metadata_batch)
                print(f'Successfully bulk added {len(messages)} memories for {speaker}: {result}')
                return

            except Exception as e:
                print(f"Bulk processing failed, falling back to individual processing: {e}")

        # Fallback to parent implementation for small batches or if bulk fails
        super().add_memories_for_speaker(speaker, messages, timestamp, desc)

    def _search_memory(self, user_id, query):
        """Search memory from Graphiti knowledge graph using best practices"""
        self._ensure_initialized()
        loop = self._get_event_loop()

        try:
            # Search with group_id for user isolation (official approach)
            user_group_id = f"user_{user_id}"

            # First, try to get user node for Node Distance Reranking
            user_node_uuid = None
            try:
                # Search for user node to enable Node Distance Reranking
                user_nodes = loop.run_until_complete(
                    self.graphiti.get_nodes_by_query(user_id, group_id=user_group_id)
                )
                if user_nodes:
                    user_node_uuid = user_nodes[0].uuid
            except Exception as e:
                print(f"Could not find user node for {user_id}: {e}")

            # Perform search with user isolation and optional Node Distance Reranking
            if user_node_uuid:
                # Use Node Distance Reranking for better user-specific results
                search_results = loop.run_until_complete(
                    self.graphiti.search(
                        query,
                        center_node_uuid=user_node_uuid,  # Node Distance Reranking
                        group_id=user_group_id,  # User isolation
                        limit=10
                    )
                )
            else:
                # Fallback to regular search with group isolation
                search_results = loop.run_until_complete(
                    self.graphiti.search(
                        query,
                        group_id=user_group_id,  # User isolation
                        limit=10
                    )
                )


            # Convert search results to the expected format
            semantic_memories = []
            graph_memories = []

            for result in search_results:
                # Extract semantic memory information
                semantic_memory = {
                    "memory": result.fact,
                    "timestamp": result.created_at.isoformat() if hasattr(result, 'created_at') and result.created_at else datetime.now(timezone.utc).isoformat(),
                    "score": getattr(result, 'score', 1.0)
                }
                semantic_memories.append(semantic_memory)

                # Extract graph relationship information if available
                if hasattr(result, 'source_node_uuid') and hasattr(result, 'target_node_uuid'):
                    # Get node information for source and target
                    try:
                        # Try to get node names, fallback to UUIDs if not available
                        source_name = getattr(result, 'source_node_name', str(result.source_node_uuid))
                        target_name = getattr(result, 'target_node_name', str(result.target_node_uuid))

                        graph_memory = {
                            "source": source_name,
                            "relationship": result.fact,
                            "target": target_name
                        }
                        graph_memories.append(graph_memory)
                    except Exception as e:
                        print(f"Warning: Could not extract graph relationship: {e}")

            # Return results in the expected format
            if not self.is_graph:
                return semantic_memories, None
            else:
                return semantic_memories, graph_memories

        except Exception as e:
            print(f"Error searching memory in Graphiti: {e}")
            # Return empty results on error
            return [], [] if self.is_graph else None

    def build_communities(self, group_id=None):
        """Build communities for better graph organization (optional optimization)"""
        self._ensure_initialized()
        loop = self._get_event_loop()

        try:
            # Build communities for the entire graph or specific group
            if group_id:
                # Note: group_id support may depend on Graphiti version
                result = loop.run_until_complete(
                    self.graphiti.build_communities()
                )
            else:
                result = loop.run_until_complete(
                    self.graphiti.build_communities()
                )

            print(f"Communities built successfully")
            return {"status": "success", "communities_built": True}

        except Exception as e:
            print(f"Error building communities: {e}")
            return {"status": "error", "error": str(e)}

    def get_user_communities(self, user_id):
        """Get communities for a specific user (if communities are built)"""
        self._ensure_initialized()
        loop = self._get_event_loop()

        try:
            user_group_id = f"user_{user_id}"

            # Search for communities related to the user
            from graphiti_core.search.search_config_recipes import COMMUNITY_HYBRID_SEARCH_RRF

            community_search_config = COMMUNITY_HYBRID_SEARCH_RRF.model_copy(deep=True)
            community_search_config.limit = 5

            search_results = loop.run_until_complete(
                self.graphiti._search(
                    query=f"user {user_id}",
                    group_id=user_group_id,
                    config=community_search_config
                )
            )

            communities = []
            for community in search_results.communities:
                communities.append({
                    "uuid": community.uuid,
                    "name": community.name,
                    "summary": community.summary[:200] + "..." if len(community.summary) > 200 else community.summary
                })

            return communities

        except Exception as e:
            print(f"Error getting user communities: {e}")
            return []

    def close_all_connections(self):
        """Close Graphiti connection"""
        if self.graphiti and self._initialized:
            loop = self._get_event_loop()
            try:
                loop.run_until_complete(self.graphiti.close())
                print("Closed Graphiti connection")
                self._initialized = False
                self.graphiti = None
            except Exception as e:
                print(f"Error closing Graphiti connection: {e}")

    def __del__(self):
        """Cleanup when the manager is destroyed"""
        try:
            self.close_all_connections()
        except Exception as e:
            print(f"Error during cleanup: {e}")
