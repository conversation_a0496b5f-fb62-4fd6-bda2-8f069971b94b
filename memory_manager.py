from abc import ABC, abstractmethod
from collections import defaultdict
import json
import os
from jinja2 import Template
import threading
import time
from concurrent.futures import ThreadPoolExecutor

from dotenv import load_dotenv
from openai import OpenAI
from tqdm import tqdm

from mem0.memory.main import Memory

from config.prompts import ANSWER_PROMPT, ANSWER_PROMPT_GRAPH
from config.config import config, llm_config, JUDGE_MODEL

load_dotenv()


# Update custom instructions
custom_instructions = """ 
Generate personal memories that follow these guidelines:

1. Each memory should be self-contained with complete context, including:
   - The person's name, do not use "user" while creating memories
   - Personal details (career aspirations, hobbies, life circumstances)
   - Emotional states and reactions
   - Ongoing journeys or future plans
   - Specific dates when events occurred

2. Include meaningful personal narratives focusing on:
   - Identity and self-acceptance journeys
   - Family planning and parenting
   - Creative outlets and hobbies
   - Mental health and self-care activities
   - Career aspirations and education goals
   - Important life events and milestones

3. Make each memory rich with specific details rather than general statements
   - Include timeframes (exact dates when possible)
   - Name specific activities (e.g., "charity race for mental health" rather than just "exercise")
   - Include emotional context and personal growth elements

4. Extract memories only from user messages, not incorporating assistant responses

5. Format each memory as a paragraph with a clear narrative structure that captures the person's experience, challenges, and aspirations
"""


class MemoryManager(ABC):
    def __init__(self, data_path=None, batch_size=10, is_graph=False,
                 output_path="output.json"):
        self.memory = self._init_memory_client()
        self.openai = OpenAI(
            api_key=llm_config['api_key'], base_url=llm_config['openai_base_url'])

        self.batch_size = batch_size
        self.data_path = data_path
        self.data = None
        self.is_graph = is_graph
        if data_path:
            with open(self.data_path, "r") as f:
                self.data = json.load(f)

        self.results = defaultdict(list)
        self.output_path = output_path
        self.is_graph = is_graph

        if self.is_graph:
            self.ANSWER_PROMPT = ANSWER_PROMPT_GRAPH
        else:
            self.ANSWER_PROMPT = ANSWER_PROMPT

    @abstractmethod
    def _init_memory_client(self):
        pass
     
    @abstractmethod
    def _add_memory(self, message, user_id, metadata):
        pass
    
    @abstractmethod
    def _search_memory(self, user_id, query):
        pass

    def add_memory(self, user_id, message, metadata, retries=3):
        for attempt in range(retries):
            try:
                res = self._add_memory(
                    message, user_id=user_id, metadata=metadata)
                print(f'successfully add memory for {user_id}: {res}')
                return
            except Exception as e:
                print(f"Error adding memory: {e}, message: {message}")
                if attempt < retries - 1:
                    time.sleep(1)  # Wait before retrying
                    continue
                else:
                    print(
                        f"Failed to add memory after {retries} attempts, meesage: {message}.")
                    return

    def search_memory(self, user_id, query, max_retries=3, retry_delay=1):
        start_time = time.time()
        retries = 0
        while retries < max_retries:
            print(f'searching memory for {user_id}, query: {query}')
            try:
                semantic_memories, graph_memories = self._search_memory(
                    user_id=user_id, query=query
                )
                break
            except Exception as e:
                print("Retrying...")
                retries += 1
                if retries >= max_retries:
                    raise e
                time.sleep(retry_delay)
        # print(f'get original memories: {json.dumps(memories)}')
        end_time = time.time()
        return semantic_memories, graph_memories, end_time - start_time
    
    def add_memories_for_speaker(self, speaker, messages, timestamp, desc):
        for i in tqdm(range(0, len(messages), self.batch_size), desc=desc):
            batch_messages = messages[i: i + self.batch_size]
            self.add_memory(speaker, batch_messages,
                            metadata={"timestamp": timestamp})
            # 避免触发rpm限制
            time.sleep(6)

    def process_conversation(self, item, idx):
        conversation = item["conversation"]
        speaker_a = conversation["speaker_a"]
        speaker_b = conversation["speaker_b"]

        speaker_a_user_id = f"{speaker_a}_{idx}"
        speaker_b_user_id = f"{speaker_b}_{idx}"

        # delete all memories for the two users
        self.memory.delete_all(user_id=speaker_a_user_id)
        self.memory.delete_all(user_id=speaker_b_user_id)

        conv_keys = list(conversation.keys())
        conv_keys = [key for key in conv_keys if not (
            key in ['speaker_a', 'speaker_b'] or "date" in key or "timestamp" in key)]

        print(f'****current conversation has {len(conv_keys)} sessions****')
        for key in tqdm(conv_keys, total=len(conv_keys), desc=f"====Processing conversation===="):
            if key in ['speaker_a', 'speaker_b'] or "date" in key or "timestamp" in key:
                continue

            date_time_key = key + "_date_time"
            timestamp = conversation[date_time_key]

            print(timestamp)
            if not timestamp:
                raise ValueError(f"Timestamp not found for key: {key}")

            chats = conversation[key]

            messages = []
            messages_reverse = []
            for chat in chats:
                if chat["speaker"] == speaker_a:
                    messages.append(
                        {"role": "user", "content": f"{speaker_a}: {chat['text']}"})
                    messages_reverse.append(
                        {"role": "assistant", "content": f"{speaker_a}: {chat['text']}"})
                elif chat["speaker"] == speaker_b:
                    messages.append(
                        {"role": "assistant", "content": f"{speaker_b}: {chat['text']}"})
                    messages_reverse.append(
                        {"role": "user", "content": f"{speaker_b}: {chat['text']}"})
                else:
                    raise ValueError(f"Unknown speaker: {chat['speaker']}")
            print(f'****current session has {len(messages)} messages****')
            # add memories for the two users on different threads
            thread_a = threading.Thread(
                target=self.add_memories_for_speaker,
                args=(speaker_a_user_id, messages, timestamp,
                      f"Adding Memories for Speaker A, user_id: {speaker_a_user_id}"),
            )
            thread_b = threading.Thread(
                target=self.add_memories_for_speaker,
                args=(speaker_b_user_id, messages_reverse,
                      timestamp, f"Adding Memories for Speaker B, user_id: {speaker_b_user_id}"),
            )

            thread_a.start()
            thread_b.start()
            thread_a.join()
            thread_b.join()
            time.sleep(10)

        print("Messages added successfully")

    def process_all_conversations(self, max_workers=10):
        if not self.data:
            raise ValueError(
                "No data loaded. Please set data_path and call load_data() first.")
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = [executor.submit(self.process_conversation, item, idx)
                       for idx, item in enumerate(self.data)]

            for future in futures:
                future.result()

    def answer_question(self, speaker_1_user_id, speaker_2_user_id, question, answer, category):
        speaker_1_memories, speaker_1_graph_memories, speaker_1_memory_time = self.search_memory(
            speaker_1_user_id, question
        )
        # print(f'search from memory, normal: {speaker_1_memories}, graph: {speaker_1_graph_memories}')
        speaker_2_memories, speaker_2_graph_memories, speaker_2_memory_time = self.search_memory(
            speaker_2_user_id, question
        )
        # print(f'search from memory, normal: {speaker_2_memories}, graph: {speaker_2_graph_memories}')

        search_1_memory = [
            f"{item['timestamp']}: {item['memory']}" for item in speaker_1_memories]
        search_2_memory = [
            f"{item['timestamp']}: {item['memory']}" for item in speaker_2_memories]

        template = Template(self.ANSWER_PROMPT)
        answer_prompt = template.render(
            speaker_1_user_id=speaker_1_user_id.split("_")[0],
            speaker_2_user_id=speaker_2_user_id.split("_")[0],
            speaker_1_memories=json.dumps(search_1_memory, indent=4),
            speaker_2_memories=json.dumps(search_2_memory, indent=4),
            speaker_1_graph_memories=json.dumps(
                speaker_1_graph_memories, indent=4),
            speaker_2_graph_memories=json.dumps(
                speaker_2_graph_memories, indent=4),
            question=question,
        )

        t1 = time.time()
        response = self.openai.chat.completions.create(
            model=JUDGE_MODEL, messages=[{"role": "system", "content": answer_prompt}], temperature=0.0
        )
        t2 = time.time()
        response_time = t2 - t1
        return (
            response.choices[0].message.content,
            speaker_1_memories,
            speaker_2_memories,
            speaker_1_memory_time,
            speaker_2_memory_time,
            speaker_1_graph_memories,
            speaker_2_graph_memories,
            response_time,
        )

    def process_question(self, val, speaker_a_user_id, speaker_b_user_id):
        question = val.get("question", "")
        answer = val.get("answer", "")
        category = val.get("category", -1)
        evidence = val.get("evidence", [])
        adversarial_answer = val.get("adversarial_answer", "")

        (
            response,
            speaker_1_memories,
            speaker_2_memories,
            speaker_1_memory_time,
            speaker_2_memory_time,
            speaker_1_graph_memories,
            speaker_2_graph_memories,
            response_time,
        ) = self.answer_question(speaker_a_user_id, speaker_b_user_id, question, answer, category)

        result = {
            "question": question,
            "answer": answer,
            "category": category,
            "evidence": evidence,
            "response": response,
            "adversarial_answer": adversarial_answer,
            "speaker_1_memories": speaker_1_memories,
            "speaker_2_memories": speaker_2_memories,
            "num_speaker_1_memories": len(speaker_1_memories),
            "num_speaker_2_memories": len(speaker_2_memories),
            "speaker_1_memory_time": speaker_1_memory_time,
            "speaker_2_memory_time": speaker_2_memory_time,
            "speaker_1_graph_memories": speaker_1_graph_memories,
            "speaker_2_graph_memories": speaker_2_graph_memories,
            "response_time": response_time,
        }
        print('question & answer detail: ')
        print(json.dumps(result))

        # Save results after each question is processed
        with open(self.output_path, "w") as f:
            json.dump(self.results, f, indent=4)

        return result

    def process_data_file(self, file_path):
        with open(file_path, "r") as f:
            data = json.load(f)

        for idx, item in tqdm(enumerate(data), total=len(data), desc="Processing conversations"):
            qa = item["qa"]
            conversation = item["conversation"]
            speaker_a = conversation["speaker_a"]
            speaker_b = conversation["speaker_b"]

            speaker_a_user_id = f"{speaker_a}_{idx}"
            speaker_b_user_id = f"{speaker_b}_{idx}"

            print(f'****current conversation has {len(qa)} questions****')
            for question_item in tqdm(
                qa, total=len(qa), desc=f"Processing questions for conversation {idx}", leave=False
            ):
                result = self.process_question(
                    question_item, speaker_a_user_id, speaker_b_user_id)
                self.results[idx].append(result)

                # Save results after each question is processed
                with open(self.output_path, "w") as f:
                    json.dump(self.results, f, indent=4)
                time.sleep(5)

        # Final save at the end
        with open(self.output_path, "w") as f:
            json.dump(self.results, f, indent=4)

    def process_questions_parallel(self, qa_list, speaker_a_user_id, speaker_b_user_id, max_workers=1):
        def process_single_question(val):
            result = self.process_question(
                val, speaker_a_user_id, speaker_b_user_id)
            # Save results after each question is processed
            with open(self.output_path, "w") as f:
                json.dump(self.results, f, indent=4)
            return result

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            results = list(
                tqdm(executor.map(process_single_question, qa_list),
                     total=len(qa_list), desc="Answering Questions")
            )

        # Final save at the end
        with open(self.output_path, "w") as f:
            json.dump(self.results, f, indent=4)

        return results
