#!/usr/bin/env python3
"""
Test script for GraphitiManager
"""

import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from graphiti.manager import <PERSON>raphitiMana<PERSON>


def test_graphiti_manager():
    """Test enhanced functionality of GraphitiManager with best practices"""
    print("Testing Enhanced GraphitiManager...")

    try:
        # Initialize the manager
        manager = GraphitiManager(is_graph=True)
        print("✓ GraphitiManager initialized successfully")

        # Test 1: Adding a single memory (text type)
        test_user_id = "test_user_1"
        test_message = "<PERSON> likes to play basketball on weekends."
        test_metadata = {"timestamp": "2024-01-01T10:00:00Z"}

        print(f"\n--- Test 1: Single Memory (Text) ---")
        print(f"Adding memory for user {test_user_id}...")
        result = manager._add_memory(test_message, test_user_id, test_metadata)
        print(f"✓ Memory added successfully: {result}")

        # Test 2: Adding conversation memory (message type)
        print(f"\n--- Test 2: Conversation Memory (Message Type) ---")
        conversation_messages = [
            {"role": "user", "content": "Hi, I'm looking for running shoes"},
            {"role": "assistant", "content": "Great! What's your preferred style?"},
            {"role": "user", "content": "I like lightweight shoes for daily jogging"}
        ]
        conv_metadata = {"timestamp": "2024-01-01T11:00:00Z"}

        result = manager._add_memory(conversation_messages, test_user_id, conv_metadata)
        print(f"✓ Conversation memory added successfully: {result}")

        # Test 3: Bulk memory processing
        print(f"\n--- Test 3: Bulk Memory Processing ---")
        bulk_messages = [
            "I prefer Nike shoes",
            "My shoe size is 10.5",
            "I run 5 miles daily",
            "I need good arch support",
            "Budget is around $150"
        ]
        bulk_metadata = [{"timestamp": f"2024-01-01T12:0{i}:00Z"} for i in range(len(bulk_messages))]

        result = manager._add_memory_bulk(bulk_messages, test_user_id, bulk_metadata)
        print(f"✓ Bulk memories added successfully: {result}")

        # Test 4: Enhanced search with user isolation
        print(f"\n--- Test 4: Enhanced Search with User Isolation ---")
        query = "What does the user prefer for running?"
        semantic_memories, graph_memories = manager._search_memory(test_user_id, query)

        print(f"✓ Search completed with user isolation")
        print(f"  Semantic memories found: {len(semantic_memories)}")
        if semantic_memories:
            for i, memory in enumerate(semantic_memories[:3]):  # Show top 3
                print(f"    {i+1}. {memory['memory'][:100]}... (score: {memory['score']})")

        if graph_memories:
            print(f"  Graph memories found: {len(graph_memories)}")
            for i, memory in enumerate(graph_memories[:3]):  # Show top 3
                print(f"    {i+1}. {memory['source']} -> {memory['relationship']} -> {memory['target']}")

        # Test 5: Communities (optional)
        print(f"\n--- Test 5: Communities (Optional) ---")
        try:
            communities_result = manager.build_communities()
            print(f"✓ Communities built: {communities_result}")

            user_communities = manager.get_user_communities(test_user_id)
            print(f"✓ User communities found: {len(user_communities)}")
            for i, community in enumerate(user_communities[:2]):
                print(f"    {i+1}. {community['name']}: {community['summary'][:100]}...")

        except Exception as e:
            print(f"⚠️ Communities feature not available or failed: {e}")

        # Clean up
        manager.close_all_connections()
        print("\n✓ Connections closed successfully")

        print("\n🎉 All enhanced tests passed!")

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

    return True


if __name__ == "__main__":
    success = test_graphiti_manager()
    sys.exit(0 if success else 1)
